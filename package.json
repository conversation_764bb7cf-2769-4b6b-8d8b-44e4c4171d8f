{"name": "app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@babel/parser": "^7.28.0", "@headlessui/react": "^2.2.7", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.1.1", "@number-flow/react": "^0.5.10", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@react-three/drei": "^10.4.4", "@react-three/fiber": "^9.0.0-alpha.8", "@tabler/icons-react": "^3.34.1", "@tailwindcss/typography": "^0.5.16", "@tsparticles/engine": "^3.8.1", "@tsparticles/react": "^3.0.0", "@tsparticles/slim": "^3.8.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "cobe": "^0.6.4", "date-fns": "^4.1.0", "dotted-map": "^2.2.3", "embla-carousel-auto-scroll": "^8.6.0", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "^8.6.0", "estree-walker": "2.0.2", "framer-motion": "^12.23.12", "input-otp": "^1.4.2", "lucide-react": "^0.536.0", "mini-svg-data-uri": "^1.4.4", "motion": "^12.23.12", "motion-dom": "^12.23.12", "next": "15.3.5", "next-themes": "^0.4.6", "qss": "^3.0.0", "react": "^19.0.0", "react-day-picker": "^9.8.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-fast-marquee": "^1.6.5", "react-hook-form": "^7.60.0", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-resizable-panels": "^3.0.3", "react-responsive-masonry": "^2.7.1", "react-rough-notation": "^1.0.5", "react-syntax-highlighter": "^15.6.1", "react-wrap-balancer": "^1.1.1", "recharts": "^3.0.2", "simplex-noise": "^4.0.3", "sonner": "^2.0.6", "swiper": "^11.2.10", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "three": "^0.178.0", "three-globe": "^2.43.0", "vaul": "^1.1.2", "zod": "^3.25.74"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-syntax-highlighter": "^15.5.13", "@types/three": "^0.178.0", "eslint": "^9.32.0", "eslint-config-next": "^15.4.5", "tailwindcss": "^4", "tw-animate-css": "^1.3.6", "typescript": "^5"}}