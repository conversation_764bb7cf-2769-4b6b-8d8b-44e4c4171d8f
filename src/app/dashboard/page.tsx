"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  AlertTriangle,
  Bell,
  ChevronDown,
  Search,
  Settings,
  TrendingUp,
  TrendingDown,
  BarChart3,
  Flag,
  Eye,
  ExternalLink,
  CheckCircle,
  AlertCircle,
  XCircle,
  Filter,
  Calendar,
  Tag,
} from "lucide-react";

interface CategoryHealth {
  category: string;
  avgSentiment: number;
  trend: "up" | "down" | "stable";
  trendStrength: number;
  alertLevel: "success" | "warning" | "critical";
  topMovingMetric: string;
  topMovingChange: number;
}

interface QuestionResponse {
  id: string;
  questionText: string;
  model: string;
  responseSnippet: string;
  sentiment: number;
  confidence: number;
  trend: "up" | "down" | "stable";
  tags: string[];
}

interface SourceAttribution {
  questionId: string;
  model: string;
  sentiment: number;
  primarySource: string;
  primaryAuthority: "high" | "medium" | "low";
  secondarySource: string;
  secondaryAuthority: "high" | "medium" | "low";
  tertiarySource: string;
  tertiaryAuthority: "high" | "medium" | "low";
  confidence: number;
  recommendedActions: Array<{
    action: string;
    checked: boolean;
  }>;
}

interface BenchmarkData {
  brand: string;
  sentiment: number;
  isOurs: boolean;
}

export default function Dashboard() {
  const [activeTab, setActiveTab] = useState("health-overview");
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [selectedFilters, setSelectedFilters] = useState({
    gpt4: true,
    claude: true,
    gemini: true,
    timeframe: "7d",
    tags: ["carbon-footprint", "recycling", "supply-chain"]
  });
  const [sourceInvestigatorOpen, setSourceInvestigatorOpen] = useState(false);
  const [selectedSourceData, setSelectedSourceData] = useState<SourceAttribution | null>(null);
  const [benchmarkQuestion, setBenchmarkQuestion] = useState("SUS-42");

  const categoryHealthData: CategoryHealth[] = [
    {
      category: "Product Quality",
      avgSentiment: 0.82,
      trend: "up",
      trendStrength: 5,
      alertLevel: "success",
      topMovingMetric: "Product X efficacy",
      topMovingChange: 12
    },
    {
      category: "Sustainability",
      avgSentiment: 0.45,
      trend: "down",
      trendStrength: 5,
      alertLevel: "critical",
      topMovingMetric: "Carbon footprint",
      topMovingChange: -22
    },
    {
      category: "Customer Support",
      avgSentiment: 0.78,
      trend: "up",
      trendStrength: 3,
      alertLevel: "warning",
      topMovingMetric: "Return policy clarity",
      topMovingChange: -5
    },
    {
      category: "Innovation & Tech",
      avgSentiment: 0.91,
      trend: "up",
      trendStrength: 5,
      alertLevel: "success",
      topMovingMetric: "AI in product development",
      topMovingChange: 18
    },
    {
      category: "Brand Trust & Ethics",
      avgSentiment: 0.65,
      trend: "down",
      trendStrength: 5,
      alertLevel: "critical",
      topMovingMetric: "Animal testing policies",
      topMovingChange: -15
    }
  ];

  const questionResponseData: QuestionResponse[] = [
    {
      id: "SUS-42",
      questionText: "What is Beiersdorf's record on reducing its carbon footprint?",
      model: "GPT-4",
      responseSnippet: "Beiersdorf has committed to a 30% reduction by 2025...",
      sentiment: 0.65,
      confidence: 92,
      trend: "up",
      tags: ["carbon-footprint", "sustainability"]
    },
    {
      id: "SUS-42",
      questionText: "What is Beiersdorf's record on reducing its carbon footprint?",
      model: "Claude",
      responseSnippet: "While Beiersdorf talks about goals, its Scope 3 emissions have grown...",
      sentiment: -0.31,
      confidence: 88,
      trend: "down",
      tags: ["carbon-footprint", "sustainability"]
    },
    {
      id: "SUS-56",
      questionText: "How does Nivea's packaging recycling program work?",
      model: "Gemini",
      responseSnippet: "Nivea uses 30% recycled plastic in most bottles...",
      sentiment: 0.70,
      confidence: 95,
      trend: "stable",
      tags: ["recycling", "packaging"]
    }
  ];

  const sourceAttributionData: SourceAttribution = {
    questionId: "SUS-42",
    model: "Claude",
    sentiment: -0.31,
    primarySource: "Blog Article: \"GreenwashWatch.org/beiersdorf-2024\"",
    primaryAuthority: "low",
    secondarySource: "Reddit Thread: \"r/sustainability/comments/...2023\"",
    secondaryAuthority: "low",
    tertiarySource: "News Article: \"Reuters.com/green-manufacturing/...2024\"",
    tertiaryAuthority: "high",
    confidence: 85,
    recommendedActions: [
      { action: "Request De-indexation of GreenwashWatch article via Google Search Console", checked: false },
      { action: "Amplify Positive Reuters Coverage in our Knowledge Base submissions", checked: false },
      { action: "Submit Correction to Perplexity Pages API", checked: false },
      { action: "Assign to Legal Team for Review", checked: false }
    ]
  };

  const benchmarkData: BenchmarkData[] = [
    { brand: "Beiersdorf", sentiment: 0.17, isOurs: true },
    { brand: "L'Oréal", sentiment: 0.82, isOurs: false },
    { brand: "Unilever", sentiment: 0.78, isOurs: false },
    { brand: "The Body Shop", sentiment: 0.91, isOurs: false }
  ];

  const getTrendIcon = (trend: "up" | "down" | "stable", strength: number = 3) => {
    if (trend === "up") return <TrendingUp className="h-4 w-4 text-green-600" />;
    if (trend === "down") return <TrendingDown className="h-4 w-4 text-red-600" />;
    return <div className="h-4 w-4 bg-gray-400 rounded-full" />;
  };

  const getAlertIcon = (level: "success" | "warning" | "critical") => {
    switch (level) {
      case "success": return "✅";
      case "warning": return "⚠️";
      case "critical": return "🔥";
    }
  };

  const getTrendSparkline = (trend: "up" | "down" | "stable", strength: number) => {
    const symbols = trend === "up" ? "▴" : trend === "down" ? "▾" : "▬";
    return symbols.repeat(strength);
  };

  const openSourceInvestigator = (questionId: string, model: string) => {
    setSelectedSourceData(sourceAttributionData);
    setSourceInvestigatorOpen(true);
  };

  const handleCategoryClick = (category: string) => {
    setSelectedCategory(category);
    setActiveTab("question-library");
  };

  const handleMetricClick = (metric: string) => {
    // Drill down to specific topic view
    setActiveTab("question-library");
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Top Navigation */}
      <header className="border-b border-border bg-white sticky top-0 z-50">
        <div className="flex h-16 items-center px-6">
          {/* Beiersdorf Logo */}
          <div className="flex items-center space-x-4 mr-8">
            <div className="h-8 w-8 bg-blue-900 rounded flex items-center justify-center">
              <span className="text-white font-bold text-sm">B</span>
            </div>
            <div className="text-sm text-gray-600">|</div>
            <span className="text-lg font-bold text-purple-600">SEMA AI</span>
          </div>

          {/* Main Navigation Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1">
            <TabsList className="bg-transparent border-none h-auto p-0 space-x-8">
              <TabsTrigger value="health-overview" className="bg-transparent border-none shadow-none data-[state=active]:bg-transparent data-[state=active]:text-purple-600 data-[state=active]:border-b-2 data-[state=active]:border-purple-600 rounded-none px-0 pb-4">
                Health Overview
              </TabsTrigger>
              <TabsTrigger value="question-library" className="bg-transparent border-none shadow-none data-[state=active]:bg-transparent data-[state=active]:text-purple-600 data-[state=active]:border-b-2 data-[state=active]:border-purple-600 rounded-none px-0 pb-4">
                Question Library
              </TabsTrigger>
              <TabsTrigger value="source-investigator" className="bg-transparent border-none shadow-none data-[state=active]:bg-transparent data-[state=active]:text-purple-600 data-[state=active]:border-b-2 data-[state=active]:border-purple-600 rounded-none px-0 pb-4">
                Source Investigator
              </TabsTrigger>
              <TabsTrigger value="playbooks" className="bg-transparent border-none shadow-none data-[state=active]:bg-transparent data-[state=active]:text-purple-600 data-[state=active]:border-b-2 data-[state=active]:border-purple-600 rounded-none px-0 pb-4">
                Playbooks
              </TabsTrigger>
              <TabsTrigger value="simulations" className="bg-transparent border-none shadow-none data-[state=active]:bg-transparent data-[state=active]:text-purple-600 data-[state=active]:border-b-2 data-[state=active]:border-purple-600 rounded-none px-0 pb-4">
                Simulations
              </TabsTrigger>
            </TabsList>

            {/* Right Navigation */}
            <div className="ml-auto flex items-center space-x-4">
              <Button variant="ghost" size="icon">
                <Bell className="h-5 w-5" />
              </Button>
              <Button variant="ghost" size="icon">
                <Settings className="h-5 w-5" />
              </Button>
              <Avatar className="h-8 w-8">
                <AvatarFallback>U</AvatarFallback>
              </Avatar>
            </div>
          </Tabs>

          <div className="p-6 space-y-6">
            <TabsContent value="health-overview" className="mt-0">
              {/* Panel 1: Question Category Health Matrix */}
              <Card className="border-2 border-blue-900">
                <CardHeader>
                  <CardTitle className="text-xl font-bold">Question Category Health Matrix</CardTitle>
                  <p className="text-sm text-gray-600">Gateway to anomalies and root-cause data</p>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b-2 border-gray-200">
                          <th className="text-left py-4 font-bold">Category</th>
                          <th className="text-left py-4 font-bold">Avg. Sentiment</th>
                          <th className="text-left py-4 font-bold">Trend (7d)</th>
                          <th className="text-left py-4 font-bold">Alert Level</th>
                          <th className="text-left py-4 font-bold">Top Moving Metric</th>
                        </tr>
                      </thead>
                      <tbody>
                        {categoryHealthData.map((category) => (
                          <tr 
                            key={category.category} 
                            className="border-b hover:bg-gray-50 cursor-pointer transition-colors"
                            onClick={() => handleCategoryClick(category.category)}
                          >
                            <td className="py-4 font-medium text-blue-900">{category.category}</td>
                            <td className="py-4">
                              <div className="flex items-center space-x-2">
                                <span className={`font-bold ${
                                  category.avgSentiment > 0.7 ? 'text-green-600' :
                                  category.avgSentiment > 0.5 ? 'text-yellow-600' : 'text-red-600'
                                }`}>
                                  {category.avgSentiment.toFixed(2)}
                                </span>
                                {getTrendIcon(category.trend)}
                              </div>
                            </td>
                            <td className="py-4">
                              <span className={`font-mono ${
                                category.trend === "up" ? 'text-green-600' :
                                category.trend === "down" ? 'text-red-600' : 'text-gray-600'
                              }`}>
                                {getTrendSparkline(category.trend, category.trendStrength)}
                              </span>
                            </td>
                            <td className="py-4 text-xl">
                              {getAlertIcon(category.alertLevel)}
                            </td>
                            <td className="py-4">
                              <button 
                                className="text-blue-600 hover:text-blue-800 hover:underline"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleMetricClick(category.topMovingMetric);
                                }}
                              >
                                "{category.topMovingMetric}" {category.topMovingChange > 0 ? '+' : ''}{category.topMovingChange}%
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>

              {/* Dynamic Benchmarking Panel */}
              <Card>
                <CardHeader>
                  <CardTitle>Dynamic Benchmarking</CardTitle>
                  <p className="text-sm text-gray-600">Benchmarking for Question {benchmarkQuestion}: "What is [BRAND]'s record on reducing its carbon footprint?"</p>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <h4 className="font-medium">Sentiment by Brand:</h4>
                    <div className="space-y-3">
                      {benchmarkData.map((brand) => (
                        <div key={brand.brand} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div className="flex items-center space-x-3">
                            <span className={`font-medium ${brand.isOurs ? 'text-blue-900' : 'text-gray-700'}`}>
                              {brand.brand}
                            </span>
                            {brand.isOurs && <Badge variant="outline">Our Brand</Badge>}
                          </div>
                          <div className="flex items-center space-x-4">
                            <div className="w-32 bg-gray-200 rounded-full h-2">
                              <div 
                                className={`h-2 rounded-full ${
                                  brand.sentiment > 0.7 ? 'bg-green-500' :
                                  brand.sentiment > 0.5 ? 'bg-yellow-500' : 'bg-red-500'
                                }`}
                                style={{ width: `${brand.sentiment * 100}%` }}
                              ></div>
                            </div>
                            <span className="font-bold text-sm w-12 text-right">
                              {brand.sentiment.toFixed(2)}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                    <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                      <p className="text-sm text-red-800">
                        <strong>Insight:</strong> "Beiersdorf's narrative on carbon footprint is significantly underperforming the category average (0.62) and key competitors. This is a critical reputational gap."
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="question-library" className="mt-0">
              <div className="grid grid-cols-12 gap-6">
                {/* Left Sidebar - Question Filters */}
                <Card className="col-span-3">
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Filter className="h-5 w-5" />
                      <span>Question Filters</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {/* Models */}
                    <div>
                      <h4 className="font-medium mb-3">Models:</h4>
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <Checkbox id="gpt4" checked={selectedFilters.gpt4} />
                          <label htmlFor="gpt4" className="text-sm">GPT-4</label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox id="claude" checked={selectedFilters.claude} />
                          <label htmlFor="claude" className="text-sm">Claude</label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox id="gemini" checked={selectedFilters.gemini} />
                          <label htmlFor="gemini" className="text-sm">Gemini</label>
                        </div>
                      </div>
                    </div>

                    {/* Time */}
                    <div>
                      <h4 className="font-medium mb-3">Time:</h4>
                      <div className="flex flex-wrap gap-2">
                        {["Last 24h", "7d", "30d", "Custom"].map((time) => (
                          <Button 
                            key={time}
                            variant={selectedFilters.timeframe === time.toLowerCase() ? "default" : "outline"}
                            size="sm"
                            className="text-xs"
                          >
                            {time}
                          </Button>
                        ))}
                      </div>
                    </div>

                    {/* Tags */}
                    <div>
                      <h4 className="font-medium mb-3">Tags:</h4>
                      <div className="flex flex-wrap gap-2">
                        {["carbon-footprint", "recycling", "supply-chain"].map((tag) => (
                          <Badge key={tag} variant="secondary" className="text-xs">
                            #{tag}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    {/* Search */}
                    <div>
                      <h4 className="font-medium mb-3">Search:</h4>
                      <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                        <Input 
                          className="pl-10"
                          placeholder="Search questions and responses..."
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Main Panel - Question & Response Explorer */}
                <Card className="col-span-9">
                  <CardHeader>
                    <CardTitle>Question & Response Explorer</CardTitle>
                    {selectedCategory && (
                      <p className="text-sm text-gray-600">View: {selectedCategory} → Carbon Footprint</p>
                    )}
                  </CardHeader>
                  <CardContent>
                    <div className="overflow-x-auto">
                      <table className="w-full text-sm">
                        <thead>
                          <tr className="border-b-2 border-gray-200">
                            <th className="text-left py-3 font-medium">Question ID</th>
                            <th className="text-left py-3 font-medium">Question Text</th>
                            <th className="text-left py-3 font-medium">Model</th>
                            <th className="text-left py-3 font-medium">Response Snippet</th>
                            <th className="text-left py-3 font-medium">Sentiment</th>
                            <th className="text-left py-3 font-medium">Confidence</th>
                            <th className="text-left py-3 font-medium">Trend</th>
                            <th className="text-left py-3 font-medium">Actions</th>
                          </tr>
                        </thead>
                        <tbody>
                          {questionResponseData.map((response, i) => (
                            <tr key={`${response.id}-${response.model}`} className="border-b hover:bg-gray-50">
                              <td className="py-3 font-mono text-xs">{response.id}</td>
                              <td className="py-3 max-w-xs">
                                <div className="truncate">{response.questionText}</div>
                              </td>
                              <td className="py-3">
                                <Badge variant="outline" className="text-xs">
                                  {response.model}
                                </Badge>
                              </td>
                              <td className="py-3 max-w-md">
                                <div className="truncate">{response.responseSnippet}</div>
                              </td>
                              <td className="py-3">
                                <span className={`font-medium ${
                                  response.sentiment > 0 ? 'text-green-600' : 
                                  response.sentiment < 0 ? 'text-red-600' : 'text-gray-600'
                                }`}>
                                  {response.sentiment.toFixed(2)}
                                </span>
                              </td>
                              <td className="py-3">{response.confidence}%</td>
                              <td className="py-3">
                                {getTrendIcon(response.trend)}
                              </td>
                              <td className="py-3">
                                <div className="flex items-center space-x-2">
                                  <Button variant="ghost" size="sm">
                                    <Eye className="h-4 w-4" />
                                    <span className="ml-1 text-xs">View Full</span>
                                  </Button>
                                  {response.sentiment > 0 ? (
                                    <Button variant="ghost" size="sm">
                                      <BarChart3 className="h-4 w-4" />
                                    </Button>
                                  ) : (
                                    <Button 
                                      variant="ghost" 
                                      size="sm"
                                      onClick={() => openSourceInvestigator(response.id, response.model)}
                                    >
                                      <Flag className="h-4 w-4 text-red-600" />
                                    </Button>
                                  )}
                                </div>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="source-investigator" className="mt-0">
              <Card>
                <CardHeader>
                  <CardTitle>Source Investigator</CardTitle>
                  <p className="text-sm text-gray-600">Analyze source attribution and take corrective actions</p>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-12">
                    <Search className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600">Select a flagged response from Question Library to investigate sources</p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="playbooks" className="mt-0">
              <Card>
                <CardHeader>
                  <CardTitle>Playbooks</CardTitle>
                  <p className="text-sm text-gray-600">Automated response workflows and remediation strategies</p>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-12">
                    <div className="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                      <span className="text-purple-600 font-bold">📋</span>
                    </div>
                    <p className="text-gray-600">Coming soon - Automated playbooks for common scenarios</p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="simulations" className="mt-0">
              <Card>
                <CardHeader>
                  <CardTitle>Simulations</CardTitle>
                  <p className="text-sm text-gray-600">Test scenarios and predict LLM response changes</p>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-12">
                    <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                      <span className="text-blue-600 font-bold">🧪</span>
                    </div>
                    <p className="text-gray-600">Coming soon - Scenario simulation tools</p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </div>
        </div>
      </header>

      {/* Source Investigator Modal */}
      <Dialog open={sourceInvestigatorOpen} onOpenChange={setSourceInvestigatorOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <Search className="h-5 w-5" />
              <span>Source Investigator for: [SUS-42: Claude Response - Sentiment: -0.31]</span>
            </DialogTitle>
          </DialogHeader>
          {selectedSourceData && (
            <div className="space-y-6">
              <div>
                <h4 className="font-bold mb-3">Likely Source Attribution ({selectedSourceData.confidence}% Confidence):</h4>
                <div className="space-y-3">
                  <div className="flex items-start space-x-3">
                    <Badge variant="outline" className="mt-1">Primary</Badge>
                    <div className="flex-1">
                      <div className="font-medium">{selectedSourceData.primarySource}</div>
                      <div className="text-sm text-gray-600">(Authority: {selectedSourceData.primaryAuthority})</div>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <Badge variant="outline" className="mt-1">Secondary</Badge>
                    <div className="flex-1">
                      <div className="font-medium">{selectedSourceData.secondarySource}</div>
                      <div className="text-sm text-gray-600">(Authority: {selectedSourceData.secondaryAuthority})</div>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <Badge variant="outline" className="mt-1">Tertiary</Badge>
                    <div className="flex-1">
                      <div className="font-medium">{selectedSourceData.tertiarySource}</div>
                      <div className="text-sm text-gray-600">(Authority: {selectedSourceData.tertiaryAuthority}) <em>[Note: Misinterpreted by model]</em></div>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-bold mb-3">Recommended Actions:</h4>
                <div className="space-y-3">
                  {selectedSourceData.recommendedActions.map((action, i) => (
                    <div key={i} className="flex items-start space-x-3">
                      <Checkbox id={`action-${i}`} />
                      <label htmlFor={`action-${i}`} className="text-sm flex-1 cursor-pointer">
                        {action.action}
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              <div className="flex justify-end space-x-3">
                <Button variant="outline" onClick={() => setSourceInvestigatorOpen(false)}>
                  Close
                </Button>
                <Button className="bg-purple-600 hover:bg-purple-700">
                  Execute Selected Actions
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}