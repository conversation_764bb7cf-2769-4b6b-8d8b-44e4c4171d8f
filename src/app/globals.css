@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800;900&family=JetBrains+Mono:wght@400;500;600;700&display=swap');

@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme {
  --color-background: #ffffff;
  --color-foreground: #000000;

  --color-card: #ffffff;
  --color-card-foreground: #000000;

  --color-popover: #ffffff;
  --color-popover-foreground: #000000;

  --color-primary: #000000;
  --color-primary-foreground: #ffffff;

  --color-secondary: #f5f5f5;
  --color-secondary-foreground: #000000;

  --color-muted: #f5f5f5;
  --color-muted-foreground: #666666;

  --color-accent: #8b5cf6;
  --color-accent-foreground: #ffffff;

  --color-destructive: #ef4444;
  --color-destructive-foreground: #ffffff;

  --color-border: #e5e5e5;
  --color-input: #e5e5e5;
  --color-ring: #8b5cf6;

  --color-chart-1: #8b5cf6;
  --color-chart-2: #10b981;
  --color-chart-3: #f59e0b;
  --color-chart-4: #000000;
  --color-chart-5: #666666;

  --color-sidebar: #f7f7f7;
  --color-sidebar-foreground: #404040;
  --color-sidebar-primary: #000000;
  --color-sidebar-primary-foreground: #ffffff;
  --color-sidebar-accent: #f0f0f0;
  --color-sidebar-accent-foreground: #000000;
  --color-sidebar-border: #e5e5e5;
  --color-sidebar-ring: #8b5cf6;

  --color-brand-purple: #8b5cf6;
  --color-text-secondary: #666666;
  --color-success: #10b981;
  --color-warning: #f59e0b;

  --font-display: "Inter", system-ui, sans-serif;
  --font-body: "Inter", system-ui, sans-serif;
  --font-mono: "JetBrains Mono", monospace;

  --radius-lg: 0.5rem;
  --radius-md: calc(0.5rem - 2px);
  --radius-sm: calc(0.5rem - 4px);

  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;

  @keyframes accordion-down {
    from {
      height: 0;
    }
    to {
      height: var(--radix-accordion-content-height);
    }
  }
  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }
    to {
      height: 0;
    }
  }
}

@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-border);
  }
}

@layer utilities {
  body {
    font-family: var(--font-body);
  }
}

@layer base {
  :root {
    --background: #ffffff;
    --foreground: #000000;
    --card: #ffffff;
    --card-foreground: #000000;
    --popover: #ffffff;
    --popover-foreground: #000000;
    --primary: #000000;
    --primary-foreground: #ffffff;
    --secondary: #f5f5f5;
    --secondary-foreground: #000000;
    --muted: #f5f5f5;
    --muted-foreground: #666666;
    --accent: #8b5cf6;
    --accent-foreground: #ffffff;
    --destructive: #ef4444;
    --destructive-foreground: #ffffff;
    --border: #e5e5e5;
    --input: #e5e5e5;
    --ring: #8b5cf6;
    --chart-1: #8b5cf6;
    --chart-2: #10b981;
    --chart-3: #f59e0b;
    --chart-4: #000000;
    --chart-5: #666666;
    --radius: 0.5rem;
    --sidebar-background: #f7f7f7;
    --sidebar-foreground: #404040;
    --sidebar-primary: #000000;
    --sidebar-primary-foreground: #ffffff;
    --sidebar-accent: #f0f0f0;
    --sidebar-accent-foreground: #000000;
    --sidebar-border: #e5e5e5;
    --sidebar-ring: #8b5cf6;
  }
}

@layer base {
  * {
    border-color: var(--border);
  }

  body {
    background-color: var(--background);
    color: var(--foreground);
    font-family: var(--font-body);
    font-weight: 400;
    line-height: 1.6;
    letter-spacing: -0.01em;
  }

  h1 {
    font-family: var(--font-display);
    font-size: 4rem;
    font-weight: 900;
    line-height: 1.1;
    letter-spacing: -0.02em;
    color: var(--color-primary);
  }

  h2 {
    font-family: var(--font-display);
    font-size: 2rem;
    font-weight: 800;
    line-height: 1.2;
    letter-spacing: -0.015em;
    color: var(--color-primary);
  }

  h3 {
    font-family: var(--font-display);
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 1.3;
    letter-spacing: -0.01em;
    color: var(--color-primary);
  }

  p {
    font-size: 0.875rem;
    color: var(--color-text-secondary);
    margin-bottom: 1rem;
  }

  .text-large {
    font-size: 1.125rem;
    line-height: 1.5;
  }

  .text-display {
    font-family: var(--font-display);
    font-size: 4rem;
    font-weight: 900;
    line-height: 1;
    letter-spacing: -0.025em;
  }

  .font-display {
    font-family: var(--font-display);
  }

  .font-body {
    font-family: var(--font-body);
  }

  .font-mono {
    font-family: var(--font-mono);
  }

  .container {
    margin-inline: auto;
    padding-inline: 2rem;
  }
}