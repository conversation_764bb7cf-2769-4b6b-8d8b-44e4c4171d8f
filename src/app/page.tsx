import { AnimatedIndicatorNavbar } from "@/components/navbars/animated-indicator-navbar";
import { MinimalCenteredHero } from "@/components/heros/minimal-centered-hero";
import { TwoColumnWithImage } from "@/components/feature/two-column-with-image";
import { ThreeColumnImageCards } from "@/components/feature/three-column-image-cards";
import { HeaderWithCardGrid } from "@/components/feature/header-with-card-grid";
import { CenteredTeamCards } from "@/components/teams/centered-team-cards";
import { GradientOverlayCta } from "@/components/cta/gradient-overlay-cta";
import { MinimalCenteredFooter } from "@/components/footers/minimal-centered-footer";

export default function HomePage() {
  return (
    <main className="min-h-screen">
      <AnimatedIndicatorNavbar />
      <MinimalCenteredHero />
      <TwoColumnWithImage />
      <ThreeColumnImageCards />
      <HeaderWithCardGrid />
      <CenteredTeamCards />
      <GradientOverlayCta />
      <MinimalCenteredFooter />
    </main>
  );
}