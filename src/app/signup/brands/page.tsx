"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Progress } from "@/components/ui/progress";
import { ArrowLeft, ArrowRight, Info } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface Brand {
  id: string;
  name: string;
  category: string;
}

interface BrandCategory {
  name: string;
  brands: Brand[];
}

const brandMatrix: BrandCategory[] = [
  {
    name: "Core Skincare",
    brands: [
      { id: "nivea", name: "NIVEA", category: "Core Skincare" },
      { id: "coppertone", name: "Coppertone", category: "Core Skincare" },
      { id: "8x4", name: "8x4", category: "Core Skincare" },
    ],
  },
  {
    name: "Specialist",
    brands: [
      { id: "eucerin", name: "Eucerin", category: "Specialist" },
      { id: "hansaplast", name: "Hansaplast", category: "Specialist" },
    ],
  },
  {
    name: "Luxury",
    brands: [
      { id: "la-prairie", name: "La Prairie", category: "Luxury" },
      { id: "chantecaille", name: "Chantecaille", category: "Luxury" },
    ],
  },
  {
    name: "Body Care",
    brands: [
      { id: "labello", name: "Labello", category: "Body Care" },
      { id: "florena", name: "Florena", category: "Body Care" },
      { id: "hidrofugal", name: "Hidrofugal", category: "Body Care" },
    ],
  },
];

export default function BrandSelectionPage() {
  const router = useRouter();
  const [selectedBrands, setSelectedBrands] = useState<Set<string>>(new Set());

  const handleBrandToggle = (brandId: string) => {
    const newSelectedBrands = new Set(selectedBrands);
    if (newSelectedBrands.has(brandId)) {
      newSelectedBrands.delete(brandId);
    } else {
      newSelectedBrands.add(brandId);
    }
    setSelectedBrands(newSelectedBrands);
  };

  const handleContinue = () => {
    if (selectedBrands.size >= 3) {
      router.push("/signup/competitors");
    }
  };

  const handleBack = () => {
    router.push("/signup");
  };

  const isValidSelection = selectedBrands.size >= 3 && selectedBrands.size <= 5;

  return (
    <div className="min-h-screen bg-background">
      <div className="container max-w-4xl mx-auto px-6 py-8">
        {/* Progress Bar */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-muted-foreground">Step 2 of 5</span>
            <span className="text-sm text-muted-foreground">40%</span>
          </div>
          <Progress value={40} className="h-2" />
        </div>

        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-black mb-4">Select Your Beiersdorf Brands</h1>
          <div className="flex items-center justify-center gap-2">
            <p className="text-lg text-muted-foreground">
              Which Beiersdorf brands should we monitor? (Select 3-5)
            </p>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger>
                  <Info className="h-4 w-4 text-muted-foreground" />
                </TooltipTrigger>
                <TooltipContent>
                  <p>Priority given to selected brands in daily reports</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>

        {/* Brand Matrix Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          {brandMatrix.map((category) => (
            <div key={category.name} className="space-y-4">
              <h3 className="text-lg font-bold text-center border-b border-border pb-2">
                {category.name}
              </h3>
              <div className="space-y-3">
                {category.brands.map((brand) => {
                  const isSelected = selectedBrands.has(brand.id);
                  return (
                    <Card
                      key={brand.id}
                      className={`cursor-pointer transition-all duration-200 hover:shadow-md hover:scale-105 ${
                        isSelected
                          ? "border-accent bg-accent/5 shadow-sm"
                          : "border-border hover:border-accent/50"
                      }`}
                      onClick={() => handleBrandToggle(brand.id)}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-center space-x-3">
                          <Checkbox
                            checked={isSelected}
                            onChange={() => handleBrandToggle(brand.id)}
                            className="data-[state=checked]:bg-accent data-[state=checked]:border-accent"
                          />
                          <div className="flex-1">
                            <h4 className="font-semibold text-sm">{brand.name}</h4>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </div>
          ))}
        </div>

        {/* Selection Status */}
        <div className="text-center mb-8">
          <p className="text-sm text-muted-foreground">
            {selectedBrands.size} of 3-5 brands selected
            {selectedBrands.size < 3 && (
              <span className="text-warning ml-2">
                (Select at least {3 - selectedBrands.size} more)
              </span>
            )}
            {selectedBrands.size > 5 && (
              <span className="text-destructive ml-2">
                (Remove {selectedBrands.size - 5} selection{selectedBrands.size - 5 > 1 ? 's' : ''})
              </span>
            )}
          </p>
        </div>

        {/* Navigation Buttons */}
        <div className="flex justify-between">
          <Button
            variant="outline"
            onClick={handleBack}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back
          </Button>
          <Button
            onClick={handleContinue}
            disabled={!isValidSelection}
            className={`flex items-center gap-2 ${
              isValidSelection
                ? "bg-accent hover:bg-accent/90 text-accent-foreground"
                : "opacity-50 cursor-not-allowed"
            }`}
          >
            Continue
            <ArrowRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}