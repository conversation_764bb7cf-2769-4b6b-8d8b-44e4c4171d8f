"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { ArrowLeft, Plus, Check } from "lucide-react";

interface Competitor {
  name: string;
  parentCompany: string;
  category: string;
}

interface CompetitorCategory {
  name: string;
  competitors: Competitor[];
}

const competitorMatrix: CompetitorCategory[] = [
  {
    name: "Mass Market",
    competitors: [
      { name: "<PERSON>", parentCompany: "Unilever", category: "Mass Market" },
      { name: "Gar<PERSON>", parentCompany: "L'Oréal", category: "Mass Market" },
      { name: "Olay", parentCompany: "P&G", category: "Mass Market" },
      { name: "St. Ives", parentCompany: "", category: "Mass Market" },
    ],
  },
  {
    name: "Premium",
    competitors: [
      { name: "Neutrogena", parentCompany: "", category: "Premium" },
      { name: "<PERSON><PERSON>", parentCompany: "", category: "Premium" },
      { name: "Kiehl's", parentCompany: "", category: "Premium" },
      { name: "The Ordinary", parentCompany: "", category: "Premium" },
    ],
  },
  {
    name: "Dermatological",
    competitors: [
      { name: "Cetaphil", parentCompany: "", category: "Dermatological" },
      { name: "Bioderma", parentCompany: "", category: "Dermatological" },
      { name: "Avene", parentCompany: "", category: "Dermatological" },
      { name: "Cerave", parentCompany: "", category: "Dermatological" },
    ],
  },
  {
    name: "Body Care",
    competitors: [
      { name: "Vaseline", parentCompany: "", category: "Body Care" },
      { name: "Palmolive", parentCompany: "", category: "Body Care" },
      { name: "Dial", parentCompany: "", category: "Body Care" },
    ],
  },
];

export default function CompetitorTrackingPage() {
  const router = useRouter();
  const [selectedCompetitors, setSelectedCompetitors] = useState<Set<string>>(new Set());
  const [customCompetitor, setCustomCompetitor] = useState("");
  const [customCompetitors, setCustomCompetitors] = useState<string[]>([]);

  const handleCompetitorSelect = (competitorName: string) => {
    const newSelected = new Set(selectedCompetitors);
    if (newSelected.has(competitorName)) {
      newSelected.delete(competitorName);
    } else {
      newSelected.add(competitorName);
    }
    setSelectedCompetitors(newSelected);
  };

  const handleAddCustomCompetitor = () => {
    if (customCompetitor.trim() && !customCompetitors.includes(customCompetitor.trim())) {
      const newCustom = customCompetitor.trim();
      setCustomCompetitors([...customCompetitors, newCustom]);
      setSelectedCompetitors(new Set([...selectedCompetitors, newCustom]));
      setCustomCompetitor("");
    }
  };

  const handleCustomCompetitorSelect = (competitorName: string) => {
    const newSelected = new Set(selectedCompetitors);
    if (newSelected.has(competitorName)) {
      newSelected.delete(competitorName);
    } else {
      newSelected.add(competitorName);
    }
    setSelectedCompetitors(newSelected);
  };

  const canContinue = selectedCompetitors.size >= 3;

  const handleContinue = () => {
    if (canContinue) {
      router.push("/signup/scope");
    }
  };

  const handleBack = () => {
    router.push("/signup/brands");
  };

  return (
    <div className="min-h-screen bg-white">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <Button
            variant="ghost"
            onClick={handleBack}
            className="mb-6 p-0 h-auto font-medium text-gray-600 hover:text-black"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>

          <div className="mb-6">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-600">Step 3 of 5</span>
              <span className="text-sm font-medium text-gray-600">60%</span>
            </div>
            <Progress value={60} className="h-2" />
          </div>

          <h1 className="text-4xl font-bold mb-4">Track Competitor Brands</h1>
          <p className="text-lg text-gray-600 mb-8">
            Monitor competitors in your categories (select 3-5):
          </p>
        </div>

        {/* Competitor Matrix Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {competitorMatrix.map((category) => (
            <div key={category.name} className="space-y-3">
              <h3 className="text-lg font-semibold text-black mb-3">
                {category.name}
              </h3>
              <div className="space-y-2">
                {category.competitors.map((competitor) => (
                  <Card
                    key={competitor.name}
                    className={`cursor-pointer transition-all duration-200 hover:shadow-md border ${
                      selectedCompetitors.has(competitor.name)
                        ? "border-accent bg-accent/5"
                        : "border-gray-200 hover:border-gray-300"
                    }`}
                    onClick={() => handleCompetitorSelect(competitor.name)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <h4 className="font-medium text-black mb-1">
                            {competitor.name}
                          </h4>
                          {competitor.parentCompany && (
                            <p className="text-sm text-gray-500">
                              {competitor.parentCompany}
                            </p>
                          )}
                        </div>
                        {selectedCompetitors.has(competitor.name) && (
                          <div className="ml-2 p-1 rounded-full bg-accent">
                            <Check className="h-3 w-3 text-white" />
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Custom Competitors Section */}
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-black mb-4">
            Add Custom Competitor
          </h3>
          <div className="flex gap-2 mb-4">
            <Input
              placeholder="Enter competitor name"
              value={customCompetitor}
              onChange={(e) => setCustomCompetitor(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  handleAddCustomCompetitor();
                }
              }}
              className="flex-1"
            />
            <Button
              onClick={handleAddCustomCompetitor}
              disabled={!customCompetitor.trim()}
              variant="outline"
              size="sm"
            >
              <Plus className="h-4 w-4" />
            </Button>
          </div>

          {/* Custom Competitors List */}
          {customCompetitors.length > 0 && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-gray-600 mb-2">
                Custom Competitors:
              </h4>
              <div className="flex flex-wrap gap-2">
                {customCompetitors.map((competitor) => (
                  <Card
                    key={competitor}
                    className={`cursor-pointer transition-all duration-200 hover:shadow-sm border ${
                      selectedCompetitors.has(competitor)
                        ? "border-accent bg-accent/5"
                        : "border-gray-200 hover:border-gray-300"
                    }`}
                    onClick={() => handleCustomCompetitorSelect(competitor)}
                  >
                    <CardContent className="p-3">
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium">{competitor}</span>
                        {selectedCompetitors.has(competitor) && (
                          <div className="p-1 rounded-full bg-accent">
                            <Check className="h-3 w-3 text-white" />
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Selection Summary */}
        <div className="mb-8 p-4 bg-gray-50 rounded-lg">
          <p className="text-sm text-gray-600 mb-2">
            Selected competitors: {selectedCompetitors.size}
            {selectedCompetitors.size < 3 && (
              <span className="text-red-500 ml-1">
                (Select at least 3 to continue)
              </span>
            )}
          </p>
          {selectedCompetitors.size > 0 && (
            <div className="flex flex-wrap gap-2">
              {Array.from(selectedCompetitors).map((competitor) => (
                <span
                  key={competitor}
                  className="inline-flex items-center px-2 py-1 bg-accent text-white text-xs rounded-md"
                >
                  {competitor}
                </span>
              ))}
            </div>
          )}
        </div>

        {/* Continue Button */}
        <div className="flex justify-end">
          <Button
            onClick={handleContinue}
            disabled={!canContinue}
            className={`px-8 py-3 font-medium ${
              canContinue
                ? "bg-black hover:bg-gray-800 text-white"
                : "bg-gray-200 text-gray-400 cursor-not-allowed"
            }`}
          >
            Continue
          </Button>
        </div>
      </div>
    </div>
  );
}