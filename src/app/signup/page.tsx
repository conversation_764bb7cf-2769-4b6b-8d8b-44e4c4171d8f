"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardHeader, CardContent } from "@/components/ui/card";
import { ArrowLeft, Eye, EyeOff } from "lucide-react";

interface FormData {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
}

interface FormErrors {
  firstName?: string;
  lastName?: string;
  email?: string;
  password?: string;
}

export default function UserIdentificationPage() {
  const router = useRouter();
  const [formData, setFormData] = useState<FormData>({
    firstName: "",
    lastName: "",
    email: "",
    password: ""
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [showPassword, setShowPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [useGeneralSignup, setUseGeneralSignup] = useState(false);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.firstName.trim()) {
      newErrors.firstName = "First name is required";
    }

    if (!formData.lastName.trim()) {
      newErrors.lastName = "Last name is required";
    }

    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!useGeneralSignup && !formData.email.endsWith("@beiersdorf.com")) {
      newErrors.email = "Must be a valid @beiersdorf.com email address";
    } else if (useGeneralSignup && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "Must be a valid email address";
    }

    if (!formData.password.trim()) {
      newErrors.password = "Password is required";
    } else if (formData.password.length < 8) {
      newErrors.password = "Password must be at least 8 characters";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const isFormValid = (): boolean => {
    const emailValid = useGeneralSignup 
      ? /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email.trim())
      : formData.email.endsWith("@beiersdorf.com");

    return (
      formData.firstName.trim() !== "" &&
      formData.lastName.trim() !== "" &&
      formData.email.trim() !== "" &&
      emailValid &&
      formData.password.trim() !== "" &&
      formData.password.length >= 8
    );
  };

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Navigate to next step
      router.push("/signup/brands");
    } catch (error) {
      console.error("Signup error:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-white flex flex-col">
      {/* Header */}
      <div className="p-6">
        <Link 
          href="/login" 
          className="inline-flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground transition-colors"
        >
          <ArrowLeft className="w-4 h-4" />
          Back to login
        </Link>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex items-center justify-center px-6 py-8">
        <Card className="w-full max-w-md border-border shadow-sm">
          <CardHeader className="space-y-4 text-center pb-6">
            <div className="space-y-2">
              <h1 className="text-2xl font-bold tracking-tight">
                {useGeneralSignup ? "SEMA AI Account Setup" : "Beiersdorf Brand Intelligence Setup"}
              </h1>
              <p className="text-sm text-muted-foreground">
                {useGeneralSignup ? "Welcome to SEMA AI. Create your account to get started." : "Welcome to SEMA AI. Let's configure your brand monitoring."}
              </p>
            </div>
            
            {/* Account Type Toggle */}
            <div className="flex items-center justify-center gap-4 p-4 bg-muted/50 rounded-lg">
              <Button
                type="button"
                variant={!useGeneralSignup ? "default" : "outline"}
                size="sm"
                onClick={() => setUseGeneralSignup(false)}
                className="text-xs"
              >
                Beiersdorf Account
              </Button>
              <Button
                type="button"
                variant={useGeneralSignup ? "default" : "outline"}
                size="sm"
                onClick={() => setUseGeneralSignup(true)}
                className="text-xs"
              >
                General Account
              </Button>
            </div>
            
            {/* Progress indicator */}
            <div className="flex items-center justify-center gap-2">
              <div className="w-8 h-1 bg-accent rounded-full"></div>
              <div className="w-8 h-1 bg-muted rounded-full"></div>
              <div className="w-8 h-1 bg-muted rounded-full"></div>
              <div className="w-8 h-1 bg-muted rounded-full"></div>
              <div className="w-8 h-1 bg-muted rounded-full"></div>
            </div>
            
            <div className="text-xs text-muted-foreground">
              Step 1 of 5: User Identification
            </div>
          </CardHeader>

          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              {/* First Name */}
              <div className="space-y-2">
                <Label htmlFor="firstName" className="text-sm font-medium">
                  First name *
                </Label>
                <Input
                  id="firstName"
                  type="text"
                  value={formData.firstName}
                  onChange={(e) => handleInputChange("firstName", e.target.value)}
                  className={`transition-colors ${errors.firstName ? "border-destructive focus-visible:ring-destructive" : ""}`}
                  placeholder="Enter your first name"
                />
                {errors.firstName && (
                  <p className="text-xs text-destructive">{errors.firstName}</p>
                )}
              </div>

              {/* Last Name */}
              <div className="space-y-2">
                <Label htmlFor="lastName" className="text-sm font-medium">
                  Last name *
                </Label>
                <Input
                  id="lastName"
                  type="text"
                  value={formData.lastName}
                  onChange={(e) => handleInputChange("lastName", e.target.value)}
                  className={`transition-colors ${errors.lastName ? "border-destructive focus-visible:ring-destructive" : ""}`}
                  placeholder="Enter your last name"
                />
                {errors.lastName && (
                  <p className="text-xs text-destructive">{errors.lastName}</p>
                )}
              </div>

              {/* Email */}
              <div className="space-y-2">
                <Label htmlFor="email" className="text-sm font-medium">
                  {useGeneralSignup ? "Email address *" : "Beiersdorf email *"}
                </Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange("email", e.target.value)}
                  className={`transition-colors ${errors.email ? "border-destructive focus-visible:ring-destructive" : ""}`}
                  placeholder={useGeneralSignup ? "<EMAIL>" : "<EMAIL>"}
                />
                {errors.email && (
                  <p className="text-xs text-destructive">{errors.email}</p>
                )}
              </div>

              {/* Password */}
              <div className="space-y-2">
                <Label htmlFor="password" className="text-sm font-medium">
                  Password *
                </Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    value={formData.password}
                    onChange={(e) => handleInputChange("password", e.target.value)}
                    className={`pr-10 transition-colors ${errors.password ? "border-destructive focus-visible:ring-destructive" : ""}`}
                    placeholder="Create a password"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4 text-muted-foreground" />
                    ) : (
                      <Eye className="h-4 w-4 text-muted-foreground" />
                    )}
                  </Button>
                </div>
                {errors.password && (
                  <p className="text-xs text-destructive">{errors.password}</p>
                )}
              </div>

              {/* Submit Button */}
              <Button
                type="submit"
                className="w-full bg-primary hover:bg-primary/90 text-primary-foreground mt-6"
                disabled={!isFormValid() || isSubmitting}
              >
                {isSubmitting 
                  ? "Creating account..." 
                  : useGeneralSignup 
                    ? "Create Account" 
                    : "Continue as Beiersdorf User"
                }
              </Button>

              {/* SSO Link - Only show for Beiersdorf accounts */}
              {!useGeneralSignup && (
                <div className="text-center pt-4">
                  <Link 
                    href="/sso" 
                    className="text-sm text-accent hover:text-accent/80 transition-colors underline underline-offset-4"
                  >
                    Using SSO? Sign in with Beiersdorf ID
                  </Link>
                </div>
              )}
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}