"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { ArrowLeft, ChevronDown, ChevronUp } from "lucide-react";

interface MonitoringParams {
  priorityMarkets: {
    regions: string[];
    countries: string[];
  };
  keyPersonas: {
    consumer: string[];
    professional: string[];
  };
  competitorBenchmarking: {
    compareSentiment: boolean;
    trackShareOfVoice: boolean;
    frequency: string;
  };
}

const regionCountries = {
  EMEA: ["United Kingdom", "Germany", "France", "Italy", "Spain", "Netherlands", "Sweden", "Switzerland"],
  NA: ["United States", "Canada", "Mexico"],
  APAC: ["China", "Japan", "South Korea", "Australia", "India", "Singapore", "Thailand"],
  LATAM: ["Brazil", "Argentina", "Chile", "Colombia", "Peru", "Uruguay"]
};

export default function StrategicMonitoringPage() {
  const router = useRouter();
  const [expandedRegions, setExpandedRegions] = useState<string[]>([]);
  const [params, setParams] = useState<MonitoringParams>({
    priorityMarkets: {
      regions: [],
      countries: []
    },
    keyPersonas: {
      consumer: [],
      professional: []
    },
    competitorBenchmarking: {
      compareSentiment: false,
      trackShareOfVoice: false,
      frequency: "daily"
    }
  });

  const handleRegionToggle = (region: string) => {
    const newRegions = params.priorityMarkets.regions.includes(region)
      ? params.priorityMarkets.regions.filter(r => r !== region)
      : [...params.priorityMarkets.regions, region];
    
    setParams(prev => ({
      ...prev,
      priorityMarkets: {
        ...prev.priorityMarkets,
        regions: newRegions
      }
    }));

    // Auto-expand/collapse region
    if (newRegions.includes(region) && !expandedRegions.includes(region)) {
      setExpandedRegions(prev => [...prev, region]);
    } else if (!newRegions.includes(region)) {
      setExpandedRegions(prev => prev.filter(r => r !== region));
      // Remove countries from that region
      const regionCountriesToRemove = regionCountries[region as keyof typeof regionCountries];
      setParams(prev => ({
        ...prev,
        priorityMarkets: {
          ...prev.priorityMarkets,
          countries: prev.priorityMarkets.countries.filter(c => !regionCountriesToRemove.includes(c))
        }
      }));
    }
  };

  const handleCountryToggle = (country: string) => {
    const newCountries = params.priorityMarkets.countries.includes(country)
      ? params.priorityMarkets.countries.filter(c => c !== country)
      : [...params.priorityMarkets.countries, country];
    
    setParams(prev => ({
      ...prev,
      priorityMarkets: {
        ...prev.priorityMarkets,
        countries: newCountries
      }
    }));
  };

  const handlePersonaToggle = (type: 'consumer' | 'professional', persona: string) => {
    const newPersonas = params.keyPersonas[type].includes(persona)
      ? params.keyPersonas[type].filter(p => p !== persona)
      : [...params.keyPersonas[type], persona];
    
    setParams(prev => ({
      ...prev,
      keyPersonas: {
        ...prev.keyPersonas,
        [type]: newPersonas
      }
    }));
  };

  const handleBenchmarkingToggle = (field: 'compareSentiment' | 'trackShareOfVoice', value: boolean) => {
    setParams(prev => ({
      ...prev,
      competitorBenchmarking: {
        ...prev.competitorBenchmarking,
        [field]: value
      }
    }));
  };

  const handleFrequencyChange = (frequency: string) => {
    setParams(prev => ({
      ...prev,
      competitorBenchmarking: {
        ...prev.competitorBenchmarking,
        frequency
      }
    }));
  };

  const handleContinue = () => {
    router.push("/signup/summary");
  };

  const handleBack = () => {
    router.push("/signup/competitors");
  };

  return (
    <div className="min-h-screen bg-white">
      <div className="container max-w-4xl mx-auto px-6 py-8">
        {/* Progress Indicator */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-muted-foreground">Step 4 of 5</span>
            <span className="text-sm text-muted-foreground">80% Complete</span>
          </div>
          <div className="w-full bg-secondary rounded-full h-2">
            <div className="bg-accent h-2 rounded-full transition-all duration-500" style={{ width: "80%" }} />
          </div>
        </div>

        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-black mb-4">Strategic Monitoring Parameters</h1>
          <p className="text-lg text-muted-foreground">
            Focus on markets and audiences that matter most:
          </p>
        </div>

        <div className="space-y-8">
          {/* Priority Markets */}
          <Card className="border-border">
            <CardHeader>
              <CardTitle className="text-xl font-bold">Priority Markets</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Region Selector */}
              <div>
                <h4 className="font-semibold mb-3">Select Regions</h4>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  {Object.keys(regionCountries).map((region) => (
                    <Button
                      key={region}
                      variant={params.priorityMarkets.regions.includes(region) ? "default" : "outline"}
                      onClick={() => handleRegionToggle(region)}
                      className="justify-center transition-all duration-200"
                    >
                      {region}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Country Drilldown */}
              {expandedRegions.map(region => (
                <div key={region} className="space-y-3">
                  <div className="flex items-center justify-between">
                    <h4 className="font-semibold text-accent">{region} Countries</h4>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setExpandedRegions(prev => prev.filter(r => r !== region))}
                    >
                      <ChevronUp className="w-4 h-4" />
                    </Button>
                  </div>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3 pl-4 border-l-2 border-accent/20">
                    {regionCountries[region as keyof typeof regionCountries].map((country) => (
                      <div key={country} className="flex items-center space-x-2">
                        <Checkbox
                          id={country}
                          checked={params.priorityMarkets.countries.includes(country)}
                          onCheckedChange={() => handleCountryToggle(country)}
                        />
                        <Label htmlFor={country} className="text-sm cursor-pointer">
                          {country}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Key Personas */}
          <Card className="border-border">
            <CardHeader>
              <CardTitle className="text-xl font-bold">Key Personas</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Consumer Personas */}
              <div>
                <h4 className="font-semibold mb-3">Consumer Segments</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {["Gen Z", "Millennials", "Parents"].map((persona) => (
                    <div key={persona} className="flex items-center space-x-2">
                      <Checkbox
                        id={`consumer-${persona}`}
                        checked={params.keyPersonas.consumer.includes(persona)}
                        onCheckedChange={() => handlePersonaToggle('consumer', persona)}
                      />
                      <Label htmlFor={`consumer-${persona}`} className="cursor-pointer">
                        {persona}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Professional Personas */}
              <div>
                <h4 className="font-semibold mb-3">Professional Audiences</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {["Dermatologists", "Retail Buyers", "Investors"].map((persona) => (
                    <div key={persona} className="flex items-center space-x-2">
                      <Checkbox
                        id={`professional-${persona}`}
                        checked={params.keyPersonas.professional.includes(persona)}
                        onCheckedChange={() => handlePersonaToggle('professional', persona)}
                      />
                      <Label htmlFor={`professional-${persona}`} className="cursor-pointer">
                        {persona}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Competitor Benchmarking */}
          <Card className="border-border">
            <CardHeader>
              <CardTitle className="text-xl font-bold">Competitor Benchmarking</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Benchmarking Options */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="sentiment-compare" className="font-medium">
                      Compare sentiment vs. selected competitors
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      Track how your brand perception compares to competitors
                    </p>
                  </div>
                  <Switch
                    id="sentiment-compare"
                    checked={params.competitorBenchmarking.compareSentiment}
                    onCheckedChange={(checked) => handleBenchmarkingToggle('compareSentiment', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="share-voice" className="font-medium">
                      Track share-of-voice in AI conversations
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      Monitor your brand's presence in AI-generated content
                    </p>
                  </div>
                  <Switch
                    id="share-voice"
                    checked={params.competitorBenchmarking.trackShareOfVoice}
                    onCheckedChange={(checked) => handleBenchmarkingToggle('trackShareOfVoice', checked)}
                  />
                </div>
              </div>

              {/* Frequency Selection */}
              <div>
                <h4 className="font-semibold mb-3">Monitoring Frequency</h4>
                <RadioGroup
                  value={params.competitorBenchmarking.frequency}
                  onValueChange={handleFrequencyChange}
                  className="flex flex-col space-y-2"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="daily" id="daily" />
                    <Label htmlFor="daily" className="cursor-pointer">
                      Daily <span className="text-accent font-medium">(recommended)</span>
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="weekly" id="weekly" />
                    <Label htmlFor="weekly" className="cursor-pointer">
                      Weekly
                    </Label>
                  </div>
                </RadioGroup>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Navigation */}
        <div className="flex justify-between mt-8">
          <Button
            variant="outline"
            onClick={handleBack}
            className="flex items-center space-x-2"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Back</span>
          </Button>
          <Button
            onClick={handleContinue}
            className="bg-accent hover:bg-accent/90 text-white px-8"
          >
            Continue to Summary
          </Button>
        </div>
      </div>
    </div>
  );
}