"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, CheckCircle, Edit, Rocket } from "lucide-react";
import { useRouter } from "next/navigation";

interface UserProfile {
  firstName: string;
  lastName: string;
  email: string;
  company: string;
  role: string;
}

interface BrandConfig {
  primary: string[];
  secondary: string[];
}

interface CompetitorGroup {
  category: string;
  brands: string[];
}

interface ScopeConfig {
  markets: string[];
  personas: string[];
  frequency: string;
}

export default function ExecutiveSummary() {
  const router = useRouter();

  // Mock data for the executive summary
  const userProfile: UserProfile = {
    firstName: "Sarah",
    lastName: "Chen",
    email: "<EMAIL>",
    company: "Beiersdorf AG",
    role: "Brand Manager"
  };

  const brandConfig: BrandConfig = {
    primary: ["NIVEA", "<PERSON>ucerin", "La Prairie"],
    secondary: ["Labello", "Hansaplast", "Coppertone", "Aquaphor"]
  };

  const competitorGroups: CompetitorGroup[] = [
    {
      category: "Mass Market",
      brands: ["L'Oréal Paris", "Garnier", "Olay", "Neutrogena"]
    },
    {
      category: "Premium Skincare", 
      brands: ["Clinique", "Estée Lauder", "SK-II", "Lancôme"]
    },
    {
      category: "Luxury Skincare",
      brands: ["La Mer", "Sisley", "Chanel Skincare", "Dior Skincare"]
    },
    {
      category: "Derma/Medical",
      brands: ["CeraVe", "Avène", "Vichy", "La Roche-Posay"]
    }
  ];

  const scopeConfig: ScopeConfig = {
    markets: ["Global", "North America", "Europe", "APAC"],
    personas: ["Beauty Enthusiasts", "Skincare Experts", "Dermatologists", "General Consumers"],
    frequency: "Real-time monitoring with daily reports"
  };

  const handleActivate = () => {
    router.push("/dashboard");
  };

  const handleEdit = () => {
    router.push("/signup/scope");
  };

  const handleBack = () => {
    router.push("/signup/scope");
  };

  return (
    <div className="min-h-screen bg-white flex flex-col">
      {/* Progress Bar */}
      <div className="w-full bg-muted h-1">
        <div className="h-full bg-accent w-full transition-all duration-500" />
      </div>

      {/* Header */}
      <div className="border-b border-border">
        <div className="max-w-4xl mx-auto px-6 py-6">
          <div className="flex items-center justify-between mb-4">
            <Button
              variant="ghost"
              onClick={handleBack}
              className="flex items-center gap-2 text-muted-foreground hover:text-foreground"
            >
              <ArrowLeft className="w-4 h-4" />
              Back
            </Button>
            <div className="text-sm text-muted-foreground">Step 5 of 5</div>
          </div>

          <div className="text-center">
            <div className="flex items-center justify-center mb-4">
              <CheckCircle className="w-8 h-8 text-accent mr-3" />
              <h1 className="text-3xl font-display font-bold">Configuration Complete</h1>
            </div>
            <h2 className="text-2xl font-display font-semibold mb-2">
              Beiersdorf Monitoring Dashboard Setup
            </h2>
            <p className="text-muted-foreground text-lg">
              Your BrandGuard monitoring system is ready for activation
            </p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 max-w-4xl mx-auto px-6 py-8">
        <div className="grid gap-6 mb-8">
          
          {/* User Profile */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                User Profile
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <div className="text-sm font-medium text-muted-foreground mb-1">Name</div>
                  <div className="font-medium">{userProfile.firstName} {userProfile.lastName}</div>
                </div>
                <div>
                  <div className="text-sm font-medium text-muted-foreground mb-1">Contact</div>
                  <div className="font-medium">{userProfile.email}</div>
                </div>
                <div>
                  <div className="text-sm font-medium text-muted-foreground mb-1">Company</div>
                  <div className="font-medium">{userProfile.company}</div>
                </div>
                <div>
                  <div className="text-sm font-medium text-muted-foreground mb-1">Role</div>
                  <div className="font-medium">{userProfile.role}</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Beiersdorf Brands */}
          <Card>
            <CardHeader>
              <CardTitle>Beiersdorf Brands</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <div className="text-sm font-medium text-muted-foreground mb-2">Primary Brands</div>
                <div className="flex flex-wrap gap-2">
                  {brandConfig.primary.map((brand) => (
                    <Badge key={brand} variant="default" className="bg-accent text-accent-foreground">
                      {brand}
                    </Badge>
                  ))}
                </div>
              </div>
              <div>
                <div className="text-sm font-medium text-muted-foreground mb-2">Secondary Brands</div>
                <div className="flex flex-wrap gap-2">
                  {brandConfig.secondary.map((brand) => (
                    <Badge key={brand} variant="secondary">
                      {brand}
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Competitor Intelligence */}
          <Card>
            <CardHeader>
              <CardTitle>Competitor Intelligence</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {competitorGroups.map((group) => (
                <div key={group.category}>
                  <div className="text-sm font-medium text-muted-foreground mb-2">{group.category}</div>
                  <div className="flex flex-wrap gap-2">
                    {group.brands.map((brand) => (
                      <Badge key={brand} variant="outline">
                        {brand}
                      </Badge>
                    ))}
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Scope Configuration */}
          <Card>
            <CardHeader>
              <CardTitle>Monitoring Scope</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <div className="text-sm font-medium text-muted-foreground mb-2">Target Markets</div>
                  <div className="space-y-1">
                    {scopeConfig.markets.map((market) => (
                      <div key={market} className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-accent" />
                        <span className="text-sm">{market}</span>
                      </div>
                    ))}
                  </div>
                </div>
                <div>
                  <div className="text-sm font-medium text-muted-foreground mb-2">Consumer Personas</div>
                  <div className="space-y-1">
                    {scopeConfig.personas.map((persona) => (
                      <div key={persona} className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-accent" />
                        <span className="text-sm">{persona}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
              <div className="pt-4 border-t border-border">
                <div className="text-sm font-medium text-muted-foreground mb-1">Monitoring Frequency</div>
                <div className="font-medium">{scopeConfig.frequency}</div>
              </div>
            </CardContent>
          </Card>

        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <Button
            onClick={handleActivate}
            size="lg"
            className="w-full sm:w-auto bg-accent hover:bg-accent/90 text-accent-foreground px-8 py-4 text-lg font-semibold"
          >
            <Rocket className="w-5 h-5 mr-2" />
            Activate BrandGuard
          </Button>
          <Button
            variant="outline"
            onClick={handleEdit}
            className="w-full sm:w-auto"
          >
            <Edit className="w-4 h-4 mr-2" />
            Edit Parameters
          </Button>
        </div>

        {/* Success Message */}
        <div className="text-center mt-8 p-6 bg-muted/50 rounded-lg border">
          <CheckCircle className="w-12 h-12 text-accent mx-auto mb-4" />
          <h3 className="text-xl font-semibold mb-2">Congratulations!</h3>
          <p className="text-muted-foreground">
            Your BrandGuard monitoring system has been configured successfully. 
            Click "Activate BrandGuard" to begin real-time brand perception monitoring.
          </p>
        </div>
      </div>
    </div>
  );
}