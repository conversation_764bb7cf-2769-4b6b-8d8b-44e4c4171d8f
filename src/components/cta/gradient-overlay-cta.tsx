"use client";

import { But<PERSON> } from "@/components/ui/button";

const GradientOverlayCta = () => {
  return (
    <section className="py-32 bg-accent">
      <div className="container mx-auto">
        <div className="flex h-[620px] items-center justify-center overflow-hidden rounded-2xl bg-accent">
          <div className="flex flex-col gap-8 p-4 text-center">
            <h2 className="text-white text-5xl font-bold font-display">
              Ready to See How LLMs Perceive Your Brand?
            </h2>
            <p className="text-white text-lg">
              Join leading companies already using SEMA AI to understand and optimize their presence in the age of artificial intelligence.
            </p>
            <div className="flex flex-col justify-center gap-2 sm:flex-row">
              <Button size="lg" variant="default" className="bg-black text-white hover:bg-black/90">
                Apply for Early Access
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-black">
                Schedule Demo
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export { GradientOverlayCta };