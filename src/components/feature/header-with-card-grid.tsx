import { ArrowRight } from "lucide-react";

import { cn } from "@/lib/utils";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";

interface feature {
  badge: string;
  title: string;
  description: string;
}

const FEATURES: Array<feature> = [
  {
    badge: "Personas",
    title: "LLM Persona Representation",
    description:
      "See how different AI models characterize your brand personality and values",
  },
  {
    badge: "Sentiment",
    title: "Real-time Sentiment Tracking",
    description:
      "Monitor emotional associations and sentiment trends across AI responses",
  },
  {
    badge: "Analysis",
    title: "Divergence Analysis",
    description:
      "Identify inconsistencies in how different LLMs represent your brand",
  },
  {
    badge: "Alerts",
    title: "Intelligent Alerts",
    description:
      "Get notified when significant changes occur in your AI brand representation",
  },
];

const HeaderWithCardGrid = () => {
  return (
    <section className="bg-background py-32">
      <div className="container mx-auto">
        <div className="flex w-full flex-col items-start justify-between gap-4 pb-16 lg:flex-row lg:items-end">
          <div className="flex w-full max-w-[48rem] flex-1 flex-col items-start gap-5">
            <h2 className="text-[2rem] font-bold leading-none tracking-tight md:text-[2.75rem] lg:text-5xl">
              Key Features
            </h2>
            <p className="text-muted-foreground w-full max-w-[44rem] text-[1.15rem] font-medium leading-normal sm:text-xl">
              Comprehensive tools to understand and optimize your brand's presence in the AI ecosystem.
            </p>
          </div>
          <div>
            <Button className="rounded-full bg-accent hover:bg-accent/90 text-accent-foreground">
              Explore All Features
              <ArrowRight />
            </Button>
          </div>
        </div>
        <div className="grid w-full grid-cols-1 grid-rows-[auto] gap-10 md:grid-cols-2">
          {FEATURES.map((feature, i) => (
            <Card
              key={`feature-${i}`}
              className="bg-card flex w-full flex-col justify-between gap-10 rounded-[.5rem] border p-5"
            >
              <CardHeader className="flex w-full flex-col justify-between gap-4 p-0 lg:flex-row lg:items-center">
                <CardTitle className="flex w-fit items-center justify-start gap-2.5">
                  <h3 className="text-[1.75rem] font-bold leading-none tracking-tight">
                    {feature.title}
                  </h3>
                </CardTitle>
                <Badge className="bg-accent text-accent-foreground">
                  <p className="text-sm">{feature.badge}</p>
                </Badge>
              </CardHeader>
              <CardContent className="p-0">
                <p className="text-muted-foreground max-w-[20rem] text-base font-medium leading-[1.4]">
                  {feature.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export { HeaderWithCardGrid };