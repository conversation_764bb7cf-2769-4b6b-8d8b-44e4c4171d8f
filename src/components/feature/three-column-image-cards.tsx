"use client";

import { Card } from "@/components/ui/card";
import { MessageSquare, BarChart3, Users } from "lucide-react";

const features = [
  {
    title: "Brand Mentions",
    description:
      "Track when and how your company appears in AI responses",
    icon: MessageSquare,
  },
  {
    title: "Sentiment Analysis", 
    description:
      "Understand the emotional tone of AI-generated content about your brand",
    icon: BarChart3,
  },
  {
    title: "Competitive Context",
    description:
      "See how you're positioned relative to competitors in LLM responses",
    icon: Users,
  },
];

const ThreeColumnImageCards = () => {
  return (
    <section className="py-32 bg-background">
      <div className="container mx-auto">
        <div className="m-auto mb-24 max-w-xl text-center">
          <h2 className="mb-6 text-3xl font-semibold lg:text-5xl font-display">
            What SEMA AI Analyzes
          </h2>
          <p className="m-auto max-w-3xl text-lg lg:text-xl text-muted-foreground">
            Our platform continuously monitors and analyzes how major LLMs represent your brand across different contexts and queries.
          </p>
        </div>
        <div className="mt-11 grid w-full grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
          {features.map((feature, index) => {
            const IconComponent = feature.icon;
            return (
              <Card key={index} className="bg-card border border-border p-8 text-center">
                <div className="mb-6 flex justify-center">
                  <div className="flex h-16 w-16 items-center justify-center rounded-lg bg-muted">
                    <IconComponent className="h-8 w-8 text-primary" />
                  </div>
                </div>
                <div>
                  <h3 className="mb-4 font-semibold text-xl font-display">{feature.title}</h3>
                  <p className="text-muted-foreground">{feature.description}</p>
                </div>
              </Card>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export { ThreeColumnImageCards };