"use client";

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, TrendingDown } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";

const TwoColumnWithImage = () => {
  return (
    <section className="relative py-32 bg-background">
      <div className="pointer-events-none absolute inset-0 z-10 bg-[50%_0] bg-[url('https://deifkwefumgah.cloudfront.net/shadcnblocks/block/shadow-overlay.png')] bg-no-repeat"></div>
      <div className="container mx-auto p-6 md:p-16">
        <div className="grid gap-12 lg:grid-cols-4">
          <div className="flex flex-col justify-between gap-8 lg:col-span-2">
            <div>
              <h2 className="my-9 text-3xl font-medium md:text-5xl font-display">
                <span className="text-black">The Problem We're Solving</span>
                <br />
                <span className="text-black">
                  AI shapes brand perception invisibly.
                </span>
              </h2>
              <p className="text-black">
                In today's AI-driven world, large language models shape how millions discover and perceive brands. But most companies have no visibility into these AI-mediated conversations.
              </p>
            </div>
            <div className="flex flex-col gap-3">
              <div className="flex items-center gap-2 text-black">
                <Brain className="h-auto w-4" />
                LLMs influence 70% of discovery decisions
              </div>
              <Separator />
              <div className="flex items-center gap-2 text-black">
                <Eye className="h-auto w-4" />
                Brand mentions happen without your knowledge
              </div>
              <Separator />
              <div className="flex items-center gap-2 text-black">
                <AlertTriangle className="h-auto w-4" />
                Reputation risks emerge in AI responses
              </div>
              <Separator />
              <div className="flex items-center gap-2 text-black">
                <TrendingDown className="h-auto w-4" />
                Competitive positioning shifts invisibly
              </div>
              <Separator />
            </div>
          </div>
          <div className="flex aspect-square w-full items-center justify-center overflow-hidden bg-muted px-6 md:px-8 lg:col-span-2">
            <img
              src="https://images.unsplash.com/photo-1677442136019-21780ecad995?q=80&w=735&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
              alt="AI Data Visualization"
              className="max-h-[550px] w-full rounded-lg object-cover"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export { TwoColumnWithImage };