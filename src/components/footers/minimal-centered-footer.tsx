"use client";

import { ArrowUpRight } from "lucide-react";

const MinimalCenteredFooter = () => {
  const navigation = [
    { name: "Product", href: "#" },
    { name: "Features", href: "#" },
    { name: "Pricing", href: "#" },
    { name: "About", href: "#" },
    { name: "Contact", href: "#" },
  ];

  const social = [
    { name: "LinkedIn", href: "https://linkedin.com" },
    { name: "Twitter", href: "https://twitter.com" },
    { name: "GitHub", href: "https://github.com" },
  ];

  const legal = [
    { name: "Privacy Policy", href: "#" },
    { name: "Terms of Service", href: "#" },
    { name: "Security", href: "#" },
  ];

  return (
    <section className="flex flex-col items-center gap-14 py-32 bg-background">
      <nav className="container flex flex-col items-center gap-4 mx-auto">
        <ul className="flex flex-wrap items-center justify-center gap-6">
          {navigation.map((item) => (
            <li key={item.name}>
              <a
                href={item.href}
                className="font-medium text-foreground transition-colors hover:text-accent"
              >
                {item.name}
              </a>
            </li>
          ))}
          {social.map((item) => (
            <li key={item.name}>
              <a
                href={item.href}
                className="flex items-center gap-0.5 font-medium text-foreground transition-colors hover:text-accent"
                target="_blank"
                rel="noopener noreferrer"
              >
                {item.name} <ArrowUpRight className="size-4" />
              </a>
            </li>
          ))}
        </ul>
        <ul className="flex flex-wrap items-center justify-center gap-6">
          {legal.map((item) => (
            <li key={item.name}>
              <a
                href={item.href}
                className="text-sm text-muted-foreground transition-colors hover:text-accent"
              >
                {item.name}
              </a>
            </li>
          ))}
        </ul>
      </nav>
    </section>
  );
};

export { MinimalCenteredFooter };