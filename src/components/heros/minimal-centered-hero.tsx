"use client";

import { useState } from "react";
import { Dialog, DialogPanel } from "@headlessui/react";
import { Menu, X } from "lucide-react";

const navigation = [
  { name: "Home", href: "#" },
  { name: "Features", href: "#" },
  { name: "Templates", href: "#" },
  { name: "Pricing", href: "#" },
];

export function MinimalCenteredHero() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  return (
    <div className="bg-white">
      <header className="absolute inset-x-0 top-0 z-50">
        <Dialog
          open={mobileMenuOpen}
          onClose={setMobileMenuOpen}
          className="lg:hidden"
        >
          <div className="fixed inset-0 z-50" />
          <DialogPanel className="fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-white p-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10">
            <div className="flex items-center justify-between">
              <a href="#" className="-m-1.5 p-1.5">
                <span className="sr-only">SEMA AI</span>
                <img
                  alt=""
                  src="https://tailwindcss.com/plus-assets/img/logos/mark.svg?color=indigo&shade=600"
                  className="h-8 w-auto"
                />
              </a>
              <button
                type="button"
                onClick={() => setMobileMenuOpen(false)}
                className="-m-2.5 rounded-md p-2.5 text-gray-700"
              >
                <span className="sr-only">Close menu</span>
                <X aria-hidden="true" className="size-6" />
              </button>
            </div>
            <div className="mt-6 flow-root">
              <div className="-my-6 divide-y divide-gray-500/10">
                <div className="space-y-2 py-6">
                  {navigation.map((item) => (
                    <a
                      key={item.name}
                      href={item.href}
                      className="-mx-3 block rounded-lg px-3 py-2 text-base/7 font-semibold text-gray-900 hover:bg-gray-50"
                    >
                      {item.name}
                    </a>
                  ))}
                </div>
                <div className="py-6">
                  <a
                    href="#"
                    className="-mx-3 block rounded-lg px-3 py-2.5 text-base/7 font-semibold text-gray-900 hover:bg-gray-50"
                  >
                    Log in
                  </a>
                </div>
              </div>
            </div>
          </DialogPanel>
        </Dialog>
      </header>

      {/* Hero Container with improved centering */}
      <div className="relative isolate px-6 pt-14 lg:px-8">
        <div className="hero-container mx-auto max-w-4xl py-16 sm:py-24 lg:py-32">
          {/* AI-Powered Brand Intelligence Section - Improved */}
          <div className="mb-12 flex justify-center">
            <div className="relative rounded-full px-4 py-2 text-base font-medium text-gray-700 ring-1 ring-gray-900/10 hover:ring-gray-900/20 transition-all duration-200 sm:text-lg">
              AI-Powered Brand Intelligence{" "}
              <a href="#" className="font-semibold text-accent hover:text-accent/80 transition-colors">
                <span aria-hidden="true" className="absolute inset-0" />
                Learn more <span aria-hidden="true">&rarr;</span>
              </a>
            </div>
          </div>

          {/* Main Content - Enhanced centering */}
          <div className="text-center space-y-8">
            <h1 className="text-5xl font-[var(--font-display)] font-bold tracking-tight text-balance text-primary sm:text-7xl">
              Discover How LLMs See Your Company
            </h1>
            <p className="mx-auto max-w-3xl text-lg font-medium text-pretty text-muted-foreground sm:text-xl/8">
              SEMA AI reveals how large language models perceive and represent your brand across conversations, helping you understand your digital reputation in the age of AI.
            </p>

            {/* Button Container - Improved consistency */}
            <div className="flex flex-col items-center justify-center gap-4 pt-2 sm:flex-row sm:gap-6">
              <a
                href="#"
                className="rounded-md bg-accent px-6 py-3 text-sm font-semibold text-white shadow-sm hover:bg-accent/90 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-accent transition-all duration-200"
              >
                Apply for Early Access
              </a>
              {/* Ghost Button Style for Watch Demo */}
              <a
                href="#"
                className="rounded-md border-2 border-accent bg-transparent px-6 py-3 text-sm font-semibold text-accent hover:bg-accent hover:text-white transition-all duration-200 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-accent"
              >
                Watch Demo <span aria-hidden="true">→</span>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}