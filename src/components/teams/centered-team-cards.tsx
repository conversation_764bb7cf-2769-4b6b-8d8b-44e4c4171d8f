interface TeamMember {
  id: string;
  name: string;
  role: string;
  description: string;
}

interface CenteredTeamCardsProps {
  heading?: string;
  subheading?: string;
  description?: string;
  members?: TeamMember[];
}

// Updated target audience members
const targetAudiences = [
  {
    id: "marketing-leaders",
    name: "Marketing Leaders",
    role: "Strategic Brand Intelligence",
    description: "CMOs and brand managers seeking AI-age insights to navigate the evolving landscape of AI-mediated brand perception and consumer behavior.",
  },
  {
    id: "pr-professionals", 
    name: "PR Professionals",
    role: "Digital Reputation Monitoring",
    description: "Communications teams monitoring digital reputation across AI platforms, ensuring consistent brand messaging in the age of automated content generation.",
  },
  {
    id: "executive-teams",
    name: "Executive Teams", 
    role: "AI-First Strategic Planning",
    description: "C-suite leaders planning AI-first strategies who understand the critical importance of brand positioning in LLM training data and outputs.",
  },
];

const CenteredTeamCards = ({
  heading = "Built for Forward-Thinking Teams",
  description = "SEMA AI serves organizations that understand the strategic importance of AI-mediated brand perception.",
  members = targetAudiences,
}: CenteredTeamCardsProps) => {
  return (
    <section className="py-24 lg:py-32 bg-background">
      <div className="container mx-auto px-4">
        <div className="mb-16 text-center">
          <h2 className="mb-6 text-3xl font-bold tracking-tight lg:text-5xl font-display text-primary">
            {heading}
          </h2>
          <p className="text-muted-foreground mx-auto max-w-2xl text-lg leading-relaxed">
            {description}
          </p>
        </div>
        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
          {members.map((member) => (
            <div key={member.id} className="p-8 bg-card border border-border rounded-lg">
              <div className="text-center">
                <div className="mb-6">
                  <h3 className="mb-2 text-xl font-bold font-display text-primary">{member.name}</h3>
                  <p className="text-accent text-sm font-semibold mb-4">
                    {member.role}
                  </p>
                  <p className="text-muted-foreground text-sm leading-relaxed">
                    {member.description}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export { CenteredTeamCards };